import { AutoMap } from '@automapper/classes';
import { Column, Entity, OneToMany, Unique } from 'typeorm';
import { Item } from '@core/item/entities/item.entity';
import { AbstractEntity } from '@common/base.entity';

/*
 * Product entity representing a product in the system.
 * It extends the AbstractEntity class and includes properties for name, description, and a relationship with items.
 * The name field is unique, ensuring that no two products can have the same name.
 *
 */
@Entity({ name: 'product' })
@Unique(['name'])
export class Product extends AbstractEntity {
  @AutoMap()
  @Column({ name: 'name', type: 'varchar', unique: true })
  name: string;

  @AutoMap()
  @Column({ name: 'description', type: 'text', nullable: true })
  description: string;
  
  @AutoMap(() => [Item])
  @OneToMany(() => Item, (item) => item.product, { cascade: true, orphanedRowAction: 'delete' })
  items: Item[];
}
