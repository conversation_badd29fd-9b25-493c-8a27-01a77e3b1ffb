import { Test, TestingModule } from '@nestjs/testing';
import { ProductController } from './product.controller';
import { ProductService } from './product.service';
import { Mapper } from '@automapper/core';
import { getMapperToken } from '@automapper/nestjs';
import { I18nService } from 'nestjs-i18n';
import { LoggerService } from '@common/logger/logger.service';
import { Product } from './entities/product.entity';
import { Item } from '@core/item/entities/item.entity';
import { ItemDto } from '@core/item/dto/item.dto';

describe('ProductController', () => {
  let controller: ProductController;
  let productService: ProductService;
  let mapper: Mapper;
  let i18nService: I18nService;

  const mockProductService = {
    findByPkWithItems: jest.fn(),
  };

  const mockMapper = {
    mapArrayAsync: jest.fn(),
  };

  const mockI18nService = {
    t: jest.fn(),
  };

  const mockLoggerService = {
    setContext: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ProductController],
      providers: [
        { provide: ProductService, useValue: mockProductService },
        { provide: getMapperToken(), useValue: mockMapper },
        { provide: I18nService, useValue: mockI18nService },
        { provide: LoggerService, useValue: mockLoggerService },
      ],
    }).compile();

    controller = module.get<ProductController>(ProductController);
    productService = module.get<ProductService>(ProductService);
    mapper = module.get<Mapper>(getMapperToken());
    i18nService = module.get<I18nService>(I18nService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('getProductItems', () => {
    it('should return all items for a product', async () => {
      // Arrange
      const productId = 1;
      const mockProduct = {
        id: productId,
        name: 'Test Product',
        description: 'Test Description',
        items: [
          {
            id: 1,
            name: 'Item 1',
            description: 'Item 1 Description',
            price: 10.99,
            unit: 5,
          },
          {
            id: 2,
            name: 'Item 2',
            description: 'Item 2 Description',
            price: 15.99,
            unit: 3,
          },
        ],
      } as Product;

      const mockItemDtos = [
        {
          id: 1,
          name: 'Item 1',
          description: 'Item 1 Description',
          price: 10.99,
          unit: 5,
        },
        {
          id: 2,
          name: 'Item 2',
          description: 'Item 2 Description',
          price: 15.99,
          unit: 3,
        },
      ] as ItemDto[];

      mockProductService.findByPkWithItems.mockResolvedValue(mockProduct);
      mockMapper.mapArrayAsync.mockResolvedValue(mockItemDtos);
      mockI18nService.t.mockReturnValue('Items retrieved successfully');

      // Act
      const result = await controller.getProductItems(productId);

      // Assert
      expect(productService.findByPkWithItems).toHaveBeenCalledWith(productId);
      expect(mapper.mapArrayAsync).toHaveBeenCalledWith(mockProduct.items, Item, ItemDto);
      expect(i18nService.t).toHaveBeenCalledWith('message.success.retrieved', { args: { entity: 'Items' } });
      expect(result).toEqual({
        message: 'Items retrieved successfully',
        data: mockItemDtos,
      });
    });
  });
});
