import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';
import { EntityDto } from '@common/base.dto';
import { ProductDto } from '@core/product/dto/product.dto';

export class ItemDto extends EntityDto {
  @AutoMap()
  name: string;

  @AutoMap()
  description?: string;

  @AutoMap()
  price: number;

  @AutoMap()
  unit: number;

  // @AutoMap(() => ProductDto)
  @AutoMap()
  product: string;
}
