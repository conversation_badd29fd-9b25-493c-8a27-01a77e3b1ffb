# Get All Items in a Product Endpoint

## Overview
This endpoint retrieves all items associated with a specific product.

## Endpoint Details
- **URL**: `GET /v1/products/:id/items`
- **Method**: GET
- **Version**: v1

## Parameters
- `id` (path parameter): The ID of the product (integer, required)

## Response Format
```json
{
  "message": "Items retrieved successfully",
  "data": [
    {
      "id": 1,
      "name": "Item Name",
      "description": "Item Description",
      "price": 10.99,
      "unit": 5,
      "product": "Product Name",
      "status": "ACTIVE",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

## Example Usage

### Request
```bash
GET /v1/products/1/items
```

### Response
```json
{
  "message": "Items retrieved successfully",
  "data": [
    {
      "id": 1,
      "name": "Organic Apples",
      "description": "Fresh organic apples from local farms",
      "price": 4.99,
      "unit": 10,
      "product": "Fresh Fruits",
      "status": "ACTIVE",
      "createdAt": "2024-01-01T10:00:00.000Z",
      "updatedAt": "2024-01-01T10:00:00.000Z"
    },
    {
      "id": 2,
      "name": "Organic Bananas",
      "description": "Ripe organic bananas",
      "price": 3.99,
      "unit": 15,
      "product": "Fresh Fruits",
      "status": "ACTIVE",
      "createdAt": "2024-01-01T10:30:00.000Z",
      "updatedAt": "2024-01-01T10:30:00.000Z"
    }
  ]
}
```

## Error Responses

### Product Not Found (404)
```json
{
  "statusCode": 404,
  "message": "Product not found",
  "error": "Not Found"
}
```

### Invalid Product ID (400)
```json
{
  "statusCode": 400,
  "message": "Validation failed (numeric string is expected)",
  "error": "Bad Request"
}
```

## Implementation Details

### Service Method
The endpoint uses a new service method `findByPkWithItems()` that:
- Finds the product by ID
- Includes the related items using TypeORM relations
- Throws a NotFoundException if the product doesn't exist

### Controller Method
The controller method:
- Validates the product ID parameter
- Calls the service to get the product with items
- Maps the items to DTOs using AutoMapper
- Returns a standardized response format

### Testing
A comprehensive test has been added to verify:
- Successful retrieval of items for a valid product
- Proper mapping of entities to DTOs
- Correct response format
- Service method calls with correct parameters
