import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository, InjectEntityManager } from '@nestjs/typeorm';
import { Repository, EntityManager, FindManyOptions, ILike } from 'typeorm';
import { I18nService } from 'nestjs-i18n';
import { paginate, IPaginationOptions, Pagination } from 'nestjs-typeorm-paginate';
import { LoggerService } from '@common/logger/logger.service';
import { EntityServiceStrategy } from '@common/entity.service.strategy';
import { DatabaseAction } from '@common/enumerations/db_action.enum';
import { EntityStatus } from '@common/base.entity';
import { PaginationQueryParams } from '@common/types/index.type';
import { Product } from './entities/product.entity';
import { ProductValidationService } from './product.validation.service';

@Injectable()
export class ProductService implements EntityServiceStrategy<Product> {
  constructor(
    @InjectRepository(Product) private readonly productRepository: Repository<Product>,
    private readonly productValidator: ProductValidationService,
    @InjectEntityManager() private readonly entityManager: EntityManager,
    private readonly i18n: I18nService,
    private readonly logger: LoggerService,
  ) {
    this.logger.setContext(ProductService.name);
  }

  async create(data: Product): Promise<Product> {
    await this.productValidator.validate(data, DatabaseAction.CREATE);
    return await this.entityManager.save(data);
  }

  async modify(id: number, data: Product): Promise<Product> {
    await this.productValidator.validate(data, DatabaseAction.UPDATE);
    return await this.productRepository.save(data);
  }

  async findByPk(id: number): Promise<Product> {
    const product = await this.productRepository.findOneBy({ id });
    if (!product) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Product' } }));
    }
    return product;
  }

  async findByPkWithItems(id: number): Promise<Product> {
    const product = await this.productRepository.findOne({
      where: { id },
      relations: ['items'],
    });
    if (!product) {
      throw new NotFoundException(this.i18n.t('message.errors.not_found', { args: { entity: 'Product' } }));
    }
    return product;
  }

  async findAllProducts(params: PaginationQueryParams, route: string) {
    const { page, limit, search, filter } = params;
    const where = {};

    if (search) {
      where['name'] = ILike(`%${search}%`);
    }

    if (filter) {
      where['status'] = filter;
    }

    const options = {
      page,
      limit,
      route,
    };

    const queryOptions: FindManyOptions<Product> = {
      where,
      order: { createdAt: 'desc' },
      relations: ['items'],
    };

    const { items, meta, links } = await paginate(this.productRepository, options, queryOptions);

    return { items, meta, links };
  }

  async activate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const product = await this.findByPk(id);
        product.status = EntityStatus.ACTIVE;
        await this.productRepository.save(product);
      }),
    );
  }

  async deactivate(ids: number[]): Promise<void> {
    await Promise.all(
      ids.map(async (id) => {
        const product = await this.findByPk(id);
        product.status = EntityStatus.INACTIVE;
        await this.productRepository.save(product);
      }),
    );
  }

  async remove(id: number): Promise<void> {
    const product = await this.findByPk(id);
    await this.productRepository.remove(product);
  }

  /**
   * Paginate through products with optional filtering criteria.
   * @param options
   * @param where
   */
  async paginate(options: IPaginationOptions, where?: any): Promise<Pagination<Product>> {
    if (where && Object.keys(where).length > 0) {
      return paginate<Product>(this.productRepository, options, {
        where,
        order: {
          createdAt: 'DESC',
        },
        relations: ['items'],
      });
    }
    return paginate<Product>(this.productRepository, options, {
      order: {
        createdAt: 'DESC',
      },
      relations: ['items'],
    });
  }
}
